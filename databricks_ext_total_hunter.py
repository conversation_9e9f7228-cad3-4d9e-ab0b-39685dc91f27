#!/usr/bin/env python3
"""
Databricks Ext Total Hunter
Connects to Databricks and finds the exact Ext Total data from your Excel SOT
"""

import os
import pandas as pd
from databricks import sql
from dotenv import load_dotenv
import time

# Load environment variables
load_dotenv('.env')

class ExtTotalHunter:
    def __init__(self):
        """Initialize connection to Databricks"""
        self.connection = None
        self.connect()
        
        # Known distinctive values from Excel SOT
        self.distinctive_values = [23.0, 875.0, 5000.0, 953.0, 3653.0, 310.0, 4145.0, 1461.0, 2762.0, 12203.0, 255.0]
        
        # Known SOT records for exact matching
        self.sot_records = [
            ("NO02", "5020", "10998", 0.0, 23.0, 23.0),
            ("NO02", "5020", "15500", 500.0, 875.0, 375.0),
            ("NO02", "5020", "34906", 133.0, 4.0, -129.0),
            ("NO02", "5050", "7500323", 148.0, 310.0, 162.0),
            ("NO02", "5050", "7567629", 0.0, 5000.0, 5000.0),
            ("NO02", "5050", "7567650", 4072.0, 4145.0, 73.0),
            ("NO02", "5050", "7591050", 11095.771, 5250.0, -5845.771),
            ("DK01", "1020", "4309", 1461.0, 953.0, -508.0),
            ("DK01", "1020", "15500", 2762.0, 255.0, -2507.0),
            ("DK01", "1050", "7574729", 12203.0, 3653.0, -8550.0)
        ]
    
    def connect(self):
        """Connect to Databricks"""
        try:
            self.connection = sql.connect(
                server_hostname=os.getenv("DATABRICKS_HOST"),
                http_path=os.getenv("DATABRICKS_HTTP_PATH"),
                access_token=os.getenv("DATABRICKS_TOKEN")
            )
            print("✅ Connected to Databricks successfully!")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to Databricks: {e}")
            return False
    
    def get_all_databases(self):
        """Get all available databases, prioritizing inventory-related ones"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("SHOW DATABASES")
                all_databases = [row[0] for row in cursor.fetchall()]

                # Filter to focus on likely inventory/SAP databases
                priority_keywords = [
                    'sap', 'inventory', 'stock', 'brewdat', 'tech', 'logistics',
                    'supply', 'warehouse', 'material', 'plant', 'storage', 'eur_tech'
                ]

                # Prioritize databases that likely contain inventory data
                priority_databases = []
                other_databases = []

                for db in all_databases:
                    db_lower = db.lower()
                    if any(keyword in db_lower for keyword in priority_keywords):
                        priority_databases.append(db)
                    else:
                        other_databases.append(db)

                # Return priority databases first, then a limited set of others
                databases_to_search = priority_databases + other_databases[:5]  # Limit other databases

                print(f"📊 Found {len(all_databases)} total databases")
                print(f"🎯 Prioritizing {len(priority_databases)} likely inventory databases: {priority_databases}")
                print(f"📋 Will search {len(databases_to_search)} databases total")

                return databases_to_search

        except Exception as e:
            print(f"❌ Error getting databases: {e}")
            return []
    
    def get_tables_in_database(self, database):
        """Get all tables in a specific database"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"SHOW TABLES IN {database}")
                tables = [(row[0], row[1]) for row in cursor.fetchall()]  # (database, table)
                return tables
        except Exception as e:
            print(f"❌ Error getting tables from {database}: {e}")
            return []
    
    def search_for_distinctive_values(self, full_table_name):
        """Search a table for our distinctive values"""
        try:
            print(f"🔍 Searching {full_table_name}...")
            
            with self.connection.cursor() as cursor:
                # First, get table schema
                cursor.execute(f"DESCRIBE {full_table_name}")
                schema = cursor.fetchall()
                
                # Find numeric columns
                numeric_columns = []
                for row in schema:
                    col_name = row[0]
                    col_type = row[1].lower()
                    if any(t in col_type for t in ['double', 'float', 'int', 'decimal', 'bigint']):
                        numeric_columns.append(col_name)
                
                if not numeric_columns:
                    return None
                
                print(f"   📋 Numeric columns: {numeric_columns}")
                
                # Search for distinctive values in numeric columns
                found_values = {}
                
                for col_name in numeric_columns:
                    for value in self.distinctive_values:
                        try:
                            query = f"SELECT COUNT(*) FROM {full_table_name} WHERE {col_name} = {value}"
                            cursor.execute(query)
                            count = cursor.fetchone()[0]
                            
                            if count > 0:
                                if col_name not in found_values:
                                    found_values[col_name] = []
                                found_values[col_name].append(value)
                                print(f"   ✅ Found {value} in {col_name} ({count} times)")
                        except Exception as e:
                            continue
                
                if found_values:
                    return {
                        'table': full_table_name,
                        'found_values': found_values,
                        'numeric_columns': numeric_columns
                    }
                
                return None
                
        except Exception as e:
            print(f"   ❌ Error searching {full_table_name}: {e}")
            return None
    
    def analyze_promising_table(self, table_info):
        """Analyze a promising table in detail"""
        table_name = table_info['table']
        found_values = table_info['found_values']
        
        print(f"\n🎯 ANALYZING PROMISING TABLE: {table_name}")
        print("="*60)
        
        try:
            with self.connection.cursor() as cursor:
                # Get row count
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                print(f"📊 Total rows: {row_count:,}")
                
                # Show sample data
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
                sample_data = cursor.fetchall()
                
                # Get column names
                cursor.execute(f"DESCRIBE {table_name}")
                columns = [row[0] for row in cursor.fetchall()]
                
                print(f"📋 Columns: {columns}")
                print(f"🎯 Found distinctive values in: {list(found_values.keys())}")
                
                # Show sample data
                print(f"\n📄 SAMPLE DATA:")
                for i, row in enumerate(sample_data):
                    print(f"Row {i+1}: {dict(zip(columns, row))}")
                
                # Look for Nordic plant codes
                plant_columns = [col for col in columns if 'plant' in col.lower()]
                if plant_columns:
                    plant_col = plant_columns[0]
                    cursor.execute(f"SELECT DISTINCT {plant_col} FROM {table_name} WHERE {plant_col} IN ('NO02', 'DK01')")
                    nordic_plants = [row[0] for row in cursor.fetchall()]
                    
                    if nordic_plants:
                        print(f"🎯 NORDIC PLANTS FOUND: {nordic_plants}")
                        
                        # This is very promising - let's check for exact SOT matches
                        return self.check_exact_sot_matches(table_name, columns, found_values)
                
                return table_info
                
        except Exception as e:
            print(f"❌ Error analyzing {table_name}: {e}")
            return None
    
    def check_exact_sot_matches(self, table_name, columns, found_values):
        """Check for exact matches with SOT records"""
        print(f"\n🔍 CHECKING EXACT SOT MATCHES IN: {table_name}")
        print("-"*50)
        
        try:
            # Identify likely column mappings
            plant_col = next((col for col in columns if 'plant' in col.lower()), None)
            storage_col = next((col for col in columns if 'storage' in col.lower() or 'location' in col.lower()), None)
            material_col = next((col for col in columns if 'material' in col.lower() and 'description' not in col.lower()), None)
            
            # Find the most likely Ext Total column
            ext_total_col = None
            max_matches = 0
            for col_name, values in found_values.items():
                if len(values) > max_matches:
                    max_matches = len(values)
                    ext_total_col = col_name
            
            print(f"📋 Column mapping:")
            print(f"   Plant: {plant_col}")
            print(f"   Storage: {storage_col}")
            print(f"   Material: {material_col}")
            print(f"   Ext Total (likely): {ext_total_col}")
            
            if not (plant_col and material_col and ext_total_col):
                print("❌ Cannot identify required columns")
                return None
            
            # Check each SOT record
            matches = 0
            mismatches = 0
            
            with self.connection.cursor() as cursor:
                for plant, storage, material, sot_sap, sot_ext, sot_diff in self.sot_records:
                    
                    # Build query to find matching record
                    where_conditions = [f"{plant_col} = '{plant}'", f"{material_col} = '{material}'"]
                    if storage_col:
                        where_conditions.append(f"{storage_col} = '{storage}'")
                    
                    where_clause = " AND ".join(where_conditions)
                    query = f"SELECT {ext_total_col} FROM {table_name} WHERE {where_clause}"
                    
                    try:
                        cursor.execute(query)
                        result = cursor.fetchone()
                        
                        if result:
                            db_ext = result[0]
                            print(f"\n📋 {plant} | {storage} | {material}")
                            print(f"   SOT Ext: {sot_ext}")
                            print(f"   DB  Ext: {db_ext}")
                            
                            if abs(float(db_ext) - float(sot_ext)) < 0.01:  # Allow for small floating point differences
                                print(f"   ✅ PERFECT MATCH!")
                                matches += 1
                            else:
                                diff = abs(float(db_ext) - float(sot_ext))
                                print(f"   ❌ MISMATCH! Difference: {diff}")
                                mismatches += 1
                        else:
                            print(f"\n📋 {plant} | {storage} | {material}")
                            print(f"   ❌ No matching record found")
                            
                    except Exception as e:
                        print(f"   ❌ Error checking record: {e}")
                        continue
            
            print(f"\n📊 MATCH SUMMARY:")
            print(f"   ✅ Perfect matches: {matches}")
            print(f"   ❌ Mismatches: {mismatches}")
            print(f"   📊 Total checked: {len(self.sot_records)}")
            
            if matches >= len(self.sot_records) * 0.7:  # 70% match rate
                print(f"\n🏆 FOUND THE EXT TOTAL TABLE!")
                print(f"   Table: {table_name}")
                print(f"   Ext Total Column: {ext_total_col}")
                
                return {
                    'table': table_name,
                    'ext_total_column': ext_total_col,
                    'plant_column': plant_col,
                    'storage_column': storage_col,
                    'material_column': material_col,
                    'matches': matches,
                    'mismatches': mismatches,
                    'match_rate': matches / len(self.sot_records)
                }
            
            return None
            
        except Exception as e:
            print(f"❌ Error checking SOT matches: {e}")
            return None
    
    def hunt_for_ext_total(self):
        """Main function to hunt for Ext Total data"""
        print("🕵️ STARTING EXT TOTAL HUNT")
        print("="*60)
        
        # Get all databases
        databases = self.get_all_databases()
        
        if not databases:
            print("❌ No databases found")
            return None
        
        promising_tables = []
        
        # Search through databases
        for database in databases:
            print(f"\n🔍 Searching database: {database}")
            
            # Get tables in this database
            tables = self.get_tables_in_database(database)
            
            if not tables:
                continue
            
            print(f"   📊 Found {len(tables)} tables")
            
            # Search each table for distinctive values
            for db_name, table_name in tables:
                full_table_name = f"{db_name}.{table_name}"
                
                # Skip obviously irrelevant tables
                if any(skip in table_name.lower() for skip in ['log', 'temp', 'test', 'backup']):
                    continue
                
                result = self.search_for_distinctive_values(full_table_name)
                if result:
                    promising_tables.append(result)

                    # If we found many distinctive values, analyze immediately
                    if len(result['found_values']) >= 5:
                        print(f"\n🎯 Found very promising table with {len(result['found_values'])} matches!")
                        analysis = self.analyze_promising_table(result)
                        if analysis and analysis.get('match_rate', 0) >= 0.8:  # 80% match rate
                            print(f"🏆 EXCELLENT MATCH FOUND! Stopping search early.")
                            return analysis
        
        print(f"\n📊 SEARCH COMPLETE")
        print(f"Found {len(promising_tables)} promising tables")
        
        # Analyze promising tables
        best_match = None
        best_score = 0
        
        for table_info in promising_tables:
            analysis = self.analyze_promising_table(table_info)
            
            if analysis and 'match_rate' in analysis:
                if analysis['match_rate'] > best_score:
                    best_score = analysis['match_rate']
                    best_match = analysis
        
        if best_match:
            print(f"\n🏆 FINAL RESULT:")
            print(f"   Table: {best_match['table']}")
            print(f"   Ext Total Column: {best_match['ext_total_column']}")
            print(f"   Match Rate: {best_match['match_rate']:.1%}")
            
            return best_match
        else:
            print(f"\n❌ No exact matches found")
            print(f"Promising tables to check manually:")
            for table in promising_tables:
                print(f"   - {table['table']}: {table['found_values']}")
            
            return promising_tables
    
    def close(self):
        """Close the connection"""
        if self.connection:
            self.connection.close()
            print("🔌 Connection closed")

def main():
    """Main execution"""
    print("🎯 DATABRICKS EXT TOTAL HUNTER")
    print("="*60)
    
    hunter = ExtTotalHunter()
    
    try:
        result = hunter.hunt_for_ext_total()
        
        if result and isinstance(result, dict) and 'table' in result:
            print(f"\n✅ SUCCESS! Found your Ext Total data:")
            print(f"   📊 Table: {result['table']}")
            print(f"   📋 Ext Total Column: {result['ext_total_column']}")
            print(f"   🎯 Match Rate: {result['match_rate']:.1%}")
        else:
            print(f"\n⚠️ No perfect match found, but found promising candidates")
            
    except Exception as e:
        print(f"❌ Error during hunt: {e}")
    
    finally:
        hunter.close()

if __name__ == "__main__":
    main()
