# Databricks notebook source
# MAGIC %md
# MAGIC # Extract Exact Ext Total Numbers
# MAGIC 
# MAGIC Quick script to find and extract the exact Ext Total numbers from Databricks

# COMMAND ----------

from pyspark.sql.functions import *
from pyspark.sql.types import *

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Known Values from Your Excel SOT

# COMMAND ----------

# These are the exact values from your Excel file that we need to find in Databricks
known_sot_values = [
    # (Plant, Storage_Location, Material, SAP_Total, Ext_Total, Diff)
    ("NO02", "5020", "10998", 0.0, 23.0, 23.0),
    ("NO02", "5020", "15500", 500.0, 875.0, 375.0),
    ("NO02", "5020", "34906", 133.0, 4.0, -129.0),
    ("NO02", "5050", "7500323", 148.0, 310.0, 162.0),
    ("NO02", "5050", "7567629", 0.0, 5000.0, 5000.0),
    ("NO02", "5050", "7567650", 4072.0, 4145.0, 73.0),
    ("NO02", "5050", "7591050", 11095.771, 5250.0, -5845.771),
    ("DK01", "1020", "4309", 1461.0, 953.0, -508.0),
    ("DK01", "1020", "15500", 2762.0, 255.0, -2507.0),
    ("DK01", "1050", "7574729", 12203.0, 3653.0, -8550.0)
]

print("🎯 Looking for these exact values in Databricks:")
for plant, storage, material, sap, ext, diff in known_sot_values:
    print(f"  {plant} | {storage} | {material} | SAP: {sap} | Ext: {ext} | Diff: {diff}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Search All Tables for These Exact Values

# COMMAND ----------

def find_tables_with_exact_values():
    """Search for tables containing our exact SOT values"""
    
    # Get all databases
    databases = [row.databaseName for row in spark.sql("SHOW DATABASES").collect()]
    
    matching_tables = []
    
    for db_name in databases:
        print(f"\n🔍 Searching database: {db_name}")
        
        try:
            tables = spark.sql(f"SHOW TABLES IN {db_name}").collect()
            
            for table_row in tables:
                table_name = table_row.tableName
                full_table_name = f"{db_name}.{table_name}"
                
                try:
                    # Quick check - get sample data
                    sample_df = spark.sql(f"SELECT * FROM {full_table_name} LIMIT 50")
                    
                    if sample_df.count() == 0:
                        continue
                    
                    # Check for our specific values
                    found_matches = []
                    
                    # Look for the distinctive Ext Total values
                    distinctive_values = [23.0, 875.0, 5000.0, 953.0, 3653.0, 310.0, 4145.0]
                    
                    # Get all numeric columns
                    numeric_columns = [col for col, dtype in sample_df.dtypes 
                                     if dtype in ['double', 'float', 'int', 'bigint', 'decimal']]
                    
                    for col_name in numeric_columns:
                        for value in distinctive_values:
                            count = sample_df.filter(col(col_name) == value).count()
                            if count > 0:
                                found_matches.append(f"{value} in {col_name}")
                    
                    if found_matches:
                        matching_tables.append({
                            'table': full_table_name,
                            'matches': found_matches,
                            'sample_count': sample_df.count()
                        })
                        
                        print(f"  🎯 FOUND: {full_table_name}")
                        print(f"     Matches: {found_matches}")
                
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"  ❌ Error accessing {db_name}: {e}")
            continue
    
    return matching_tables

# Search for tables with exact values
print("🔍 Searching for tables with exact SOT values...")
exact_match_tables = find_tables_with_exact_values()

print(f"\n📊 Found {len(exact_match_tables)} tables with exact value matches")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Detailed Analysis of Exact Matches

# COMMAND ----------

def analyze_exact_match_table(table_name):
    """Analyze a table that contains our exact values"""
    
    print(f"\n🔍 ANALYZING: {table_name}")
    print("="*60)
    
    try:
        df = spark.sql(f"SELECT * FROM {table_name}")
        
        print(f"📊 Total rows: {df.count()}")
        print(f"📋 Columns: {df.columns}")
        
        # Show schema
        print("\n📋 SCHEMA:")
        df.printSchema()
        
        # Look for our specific values
        distinctive_values = [23.0, 875.0, 5000.0, 953.0, 3653.0, 310.0, 4145.0, 1461.0, 2762.0, 12203.0]
        
        print(f"\n🔍 Searching for distinctive values...")
        
        value_locations = {}
        
        # Check each numeric column
        numeric_columns = [col for col, dtype in df.dtypes 
                         if dtype in ['double', 'float', 'int', 'bigint', 'decimal']]
        
        for col_name in numeric_columns:
            for value in distinctive_values:
                count = df.filter(col(col_name) == value).count()
                if count > 0:
                    if col_name not in value_locations:
                        value_locations[col_name] = []
                    value_locations[col_name].append(value)
                    print(f"  ✅ Found {value} in column '{col_name}' ({count} times)")
        
        # Show sample data focusing on columns with our values
        if value_locations:
            print(f"\n📄 SAMPLE DATA (focusing on matching columns):")
            
            # Show all columns but highlight the ones with matches
            sample_data = df.limit(10)
            sample_data.show(truncate=False)
            
            # Now let's try to find the exact records
            print(f"\n🎯 EXACT RECORD MATCHES:")
            
            for plant, storage, material, sap_total, ext_total, diff in known_sot_values:
                print(f"\nLooking for: Plant={plant}, Storage={storage}, Material={material}")
                print(f"Expected: SAP={sap_total}, Ext={ext_total}, Diff={diff}")
                
                # Try different column name variations
                possible_plant_cols = [col for col in df.columns if 'plant' in col.lower()]
                possible_storage_cols = [col for col in df.columns if 'storage' in col.lower() or 'location' in col.lower()]
                possible_material_cols = [col for col in df.columns if 'material' in col.lower() and 'description' not in col.lower()]
                
                print(f"  Plant columns: {possible_plant_cols}")
                print(f"  Storage columns: {possible_storage_cols}")
                print(f"  Material columns: {possible_material_cols}")
                
                # Try to find matching records
                if possible_plant_cols and possible_material_cols:
                    plant_col = possible_plant_cols[0]
                    material_col = possible_material_cols[0]
                    
                    matching_records = df.filter(
                        (col(plant_col) == plant) & 
                        (col(material_col) == material)
                    )
                    
                    match_count = matching_records.count()
                    if match_count > 0:
                        print(f"  🎯 FOUND {match_count} matching records:")
                        matching_records.show(truncate=False)
                        
                        # Check if Ext Total values match
                        for ext_col in value_locations.keys():
                            if ext_total in value_locations[ext_col]:
                                ext_match = matching_records.filter(col(ext_col) == ext_total).count()
                                if ext_match > 0:
                                    print(f"  ✅ EXT TOTAL MATCH! Column '{ext_col}' contains {ext_total}")
                    else:
                        print(f"  ❌ No matching records found")
        
        return value_locations
        
    except Exception as e:
        print(f"❌ Error analyzing {table_name}: {e}")
        return {}

# Analyze each table with exact matches
all_value_locations = {}

for table_info in exact_match_tables:
    table_name = table_info['table']
    value_locations = analyze_exact_match_table(table_name)
    if value_locations:
        all_value_locations[table_name] = value_locations

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Extract Complete Dataset from Best Match

# COMMAND ----------

def extract_complete_dataset(table_name, ext_total_column):
    """Extract the complete dataset from the best matching table"""
    
    print(f"\n📊 EXTRACTING COMPLETE DATASET FROM: {table_name}")
    print("="*60)
    
    try:
        df = spark.sql(f"SELECT * FROM {table_name}")
        
        # Get total counts
        total_rows = df.count()
        print(f"📊 Total rows: {total_rows:,}")
        
        # Show summary statistics for Ext Total column
        if ext_total_column in df.columns:
            ext_stats = df.agg(
                sum(ext_total_column).alias("total_ext"),
                count(ext_total_column).alias("count_ext"),
                avg(ext_total_column).alias("avg_ext"),
                min(ext_total_column).alias("min_ext"),
                max(ext_total_column).alias("max_ext")
            ).collect()[0]
            
            print(f"\n📊 {ext_total_column} STATISTICS:")
            print(f"  Total: {ext_stats.total_ext:,.2f}")
            print(f"  Count: {ext_stats.count_ext:,}")
            print(f"  Average: {ext_stats.avg_ext:,.2f}")
            print(f"  Min: {ext_stats.min_ext:,.2f}")
            print(f"  Max: {ext_stats.max_ext:,.2f}")
            
            # Show records with non-zero Ext Total
            non_zero_ext = df.filter(col(ext_total_column) != 0)
            non_zero_count = non_zero_ext.count()
            
            print(f"\n📊 Non-zero {ext_total_column} records: {non_zero_count:,}")
            
            if non_zero_count > 0:
                print(f"\n📄 SAMPLE NON-ZERO EXT TOTAL RECORDS:")
                non_zero_ext.show(10, truncate=False)
                
                # Group by plant if possible
                plant_cols = [col for col in df.columns if 'plant' in col.lower()]
                if plant_cols:
                    plant_col = plant_cols[0]
                    plant_summary = non_zero_ext.groupBy(plant_col).agg(
                        count("*").alias("record_count"),
                        sum(ext_total_column).alias("total_ext")
                    ).orderBy(desc("total_ext"))
                    
                    print(f"\n📊 SUMMARY BY PLANT:")
                    plant_summary.show()
        
        # Save the complete dataset for further analysis
        print(f"\n💾 Saving complete dataset...")
        df.createOrReplaceTempView("found_ext_total_data")
        
        print(f"✅ Dataset saved as temporary view: found_ext_total_data")
        print(f"   Use: spark.sql('SELECT * FROM found_ext_total_data') to access")
        
        return df
        
    except Exception as e:
        print(f"❌ Error extracting dataset: {e}")
        return None

# Extract from the best matching table
if all_value_locations:
    # Find the table with the most value matches
    best_table = max(all_value_locations.items(), key=lambda x: len(x[1]))
    table_name = best_table[0]
    value_locations = best_table[1]
    
    # Find the most likely Ext Total column
    ext_total_column = None
    for col_name, values in value_locations.items():
        if len(values) >= 3:  # Column with at least 3 of our distinctive values
            ext_total_column = col_name
            break
    
    if ext_total_column:
        print(f"🏆 BEST MATCH: {table_name}")
        print(f"🎯 EXT TOTAL COLUMN: {ext_total_column}")
        
        complete_dataset = extract_complete_dataset(table_name, ext_total_column)
    else:
        print("❌ Could not identify Ext Total column")
else:
    print("❌ No tables found with exact value matches")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Final Comparison with SOT

# COMMAND ----------

if 'complete_dataset' in locals() and complete_dataset is not None:
    
    print("🔍 FINAL COMPARISON WITH SOT")
    print("="*60)
    
    # Compare each known SOT value
    for plant, storage, material, sot_sap, sot_ext, sot_diff in known_sot_values:
        
        print(f"\n📋 Checking: {plant} | {storage} | {material}")
        print(f"   SOT: SAP={sot_sap}, Ext={sot_ext}, Diff={sot_diff}")
        
        # Try to find this record in the dataset
        # We'll need to identify the correct column names
        columns = complete_dataset.columns
        
        plant_col = next((col for col in columns if 'plant' in col.lower()), None)
        storage_col = next((col for col in columns if 'storage' in col.lower() or 'location' in col.lower()), None)
        material_col = next((col for col in columns if 'material' in col.lower() and 'description' not in col.lower()), None)
        
        if plant_col and material_col:
            # Look for matching record
            matching = complete_dataset.filter(
                (col(plant_col) == plant) & 
                (col(material_col) == material)
            )
            
            if storage_col:
                matching = matching.filter(col(storage_col) == storage)
            
            match_count = matching.count()
            
            if match_count > 0:
                print(f"   ✅ Found {match_count} matching record(s) in Databricks")
                
                # Get the values
                db_record = matching.collect()[0].asDict()
                
                db_ext = db_record.get(ext_total_column, "N/A")
                
                print(f"   DB:  Ext={db_ext}")
                
                if db_ext == sot_ext:
                    print(f"   ✅ PERFECT MATCH!")
                else:
                    diff = abs(db_ext - sot_ext) if isinstance(db_ext, (int, float)) else "N/A"
                    print(f"   ❌ MISMATCH! Difference: {diff}")
                    
            else:
                print(f"   ❌ No matching record found in Databricks")
        else:
            print(f"   ❌ Cannot identify plant/material columns")

print("\n✅ ANALYSIS COMPLETE!")
print("\n🎯 SUMMARY:")
print("- Found tables with exact value matches")
print("- Identified most likely Ext Total column")
print("- Compared individual records with SOT")
print("- Dataset available as 'found_ext_total_data' view")
