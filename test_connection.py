import os
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# Load environment variables
load_dotenv('.env')

def test_databricks_connection():
    """Test basic Databricks connection"""
    print("Testing Databricks connection...")
    
    try:
        connection = sql.connect(
            server_hostname=os.getenv("DATABRICKS_HOST"),
            http_path=os.getenv("DATABRICKS_HTTP_PATH"),
            access_token=os.getenv("DATABRICKS_TOKEN")
        )
        
        with connection.cursor() as cursor:
            # Simple test query
            cursor.execute("SELECT 1 as test_column")
            result = cursor.fetchall()
            print("✅ Connection successful!")
            print(f"Test result: {result}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        return False

def list_available_tables():
    """List available SAP tables"""
    print("\nListing available SAP tables...")
    
    try:
        connection = sql.connect(
            server_hostname=os.getenv("DATABRICKS_HOST"),
            http_path=os.getenv("DATABRICKS_HTTP_PATH"),
            access_token=os.getenv("DATABRICKS_TOKEN")
        )
        
        with connection.cursor() as cursor:
            query = """
            SHOW TABLES IN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe
            """
            cursor.execute(query)
            tables = cursor.fetchall()
            
            print(f"Found {len(tables)} tables:")
            sap_tables = ['mara', 'makt', 'mbew', 'bseg', 'bkpf', 'vbrp', 'vbrk', 'glpct']
            
            for table in tables:
                table_name = table[1] if len(table) > 1 else table[0]
                if any(sap_table in table_name.lower() for sap_table in sap_tables):
                    print(f"  ✅ {table_name} (relevant)")
                else:
                    print(f"     {table_name}")
            
        connection.close()
        
    except Exception as e:
        print(f"❌ Error listing tables: {str(e)}")

def test_simple_query():
    """Test a simple query on one of the tables"""
    print("\nTesting simple query on MARA table...")
    
    try:
        connection = sql.connect(
            server_hostname=os.getenv("DATABRICKS_HOST"),
            http_path=os.getenv("DATABRICKS_HTTP_PATH"),
            access_token=os.getenv("DATABRICKS_TOKEN")
        )
        
        with connection.cursor() as cursor:
            query = """
            SELECT MATNR, MTART 
            FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mara 
            LIMIT 5
            """
            cursor.execute(query)
            result = cursor.fetchall()
            
            print("✅ Query successful!")
            print("Sample materials:")
            for row in result:
                print(f"  Material: {row[0]}, Type: {row[1]}")
                
        connection.close()
        
    except Exception as e:
        print(f"❌ Query failed: {str(e)}")

if __name__ == "__main__":
    print("🔍 Testing Databricks SAP Connection")
    
    # Test basic connection
    if test_databricks_connection():
        # List tables
        list_available_tables()
        
        # Test simple query
        test_simple_query()
    else:
        print("Cannot proceed - connection failed")

