import os
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# Load environment variables
load_dotenv('.env')

def test_databricks_connection():
    """Test basic Databricks connection"""
    print("Testing Databricks connection...")
    
    try:
        connection = sql.connect(
            server_hostname=os.getenv("DATABRICKS_HOST"),
            http_path=os.getenv("DATABRICKS_HTTP_PATH"),
            access_token=os.getenv("DATABRICKS_TOKEN")
        )
        
        with connection.cursor() as cursor:
            # Simple test query
            cursor.execute("SELECT 1 as test_column")
            result = cursor.fetchall()
            print("✅ Connection successful!")
            print(f"Test result: {result}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        return False

def list_available_tables():
    """List available SAP tables"""
    print("\nListing available SAP tables...")
    
    try:
        connection = sql.connect(
            server_hostname=os.getenv("DATABRICKS_HOST"),
            http_path=os.getenv("DATABRICKS_HTTP_PATH"),
            access_token=os.getenv("DATABRICKS_TOKEN")
        )
        
        with connection.cursor() as cursor:
            query = """
            SHOW TABLES IN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe
            """
            cursor.execute(query)
            tables = cursor.fetchall()
            
            print(f"Found {len(tables)} tables:")
            sap_tables = ['mara', 'makt', 'mbew', 'bseg', 'bkpf', 'vbrp', 'vbrk', 'glpct']
            
            for table in tables:
                table_name = table[1] if len(table) > 1 else table[0]
                if any(sap_table in table_name.lower() for sap_table in sap_tables):
                    print(f"  ✅ {table_name} (relevant)")
                else:
                    print(f"     {table_name}")
            
        connection.close()
        
    except Exception as e:
        print(f"❌ Error listing tables: {str(e)}")

def search_for_ext_total():
    """Search for Ext Total values in SAP tables"""
    print("\nSearching for Ext Total values in SAP tables...")

    # Our distinctive values from the Excel SOT
    distinctive_values = [23.0, 875.0, 5000.0, 953.0, 3653.0, 310.0, 4145.0, 1461.0, 2762.0, 12203.0, 255.0]

    try:
        connection = sql.connect(
            server_hostname=os.getenv("DATABRICKS_HOST"),
            http_path=os.getenv("DATABRICKS_HTTP_PATH"),
            access_token=os.getenv("DATABRICKS_TOKEN")
        )

        with connection.cursor() as cursor:
            # First, let's see what tables are available
            cursor.execute("SHOW TABLES IN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe")
            tables = cursor.fetchall()

            print(f"Found {len(tables)} tables in SAP database")

            # Look for inventory-related tables
            inventory_tables = []
            for table in tables:
                table_name = table[1] if len(table) > 1 else table[0]
                # SAP inventory tables: MARD (storage location stock), MBEW (material valuation), MCHB (batch stock)
                if any(keyword in table_name.lower() for keyword in ['mard', 'mbew', 'mchb', 'stock', 'inventory']):
                    inventory_tables.append(table_name)
                    print(f"  🎯 Found inventory table: {table_name}")

            if not inventory_tables:
                print("❌ No obvious inventory tables found. Checking all tables...")
                inventory_tables = [table[1] if len(table) > 1 else table[0] for table in tables[:10]]  # Check first 10 tables

            # Search each inventory table for our distinctive values
            for table_name in inventory_tables:
                print(f"\n🔍 Searching table: {table_name}")
                full_table_name = f"brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.{table_name}"

                try:
                    # Get table schema
                    cursor.execute(f"DESCRIBE {full_table_name}")
                    columns = cursor.fetchall()

                    # Find numeric columns
                    numeric_columns = []
                    for col in columns:
                        col_name = col[0]
                        col_type = col[1].lower()
                        if any(t in col_type for t in ['int', 'float', 'double', 'decimal', 'numeric']):
                            numeric_columns.append(col_name)

                    print(f"   📋 Found {len(numeric_columns)} numeric columns: {numeric_columns[:5]}...")

                    # Search for distinctive values
                    found_values = []
                    for value in distinctive_values:
                        for col in numeric_columns[:10]:  # Check first 10 numeric columns
                            try:
                                query = f"SELECT COUNT(*) FROM {full_table_name} WHERE {col} = {value}"
                                cursor.execute(query)
                                count = cursor.fetchone()[0]

                                if count > 0:
                                    found_values.append((value, col, count))
                                    print(f"   ✅ Found {value} in column {col} ({count} rows)")

                            except Exception as e:
                                continue  # Skip problematic columns

                    if len(found_values) >= 3:
                        print(f"   🏆 PROMISING TABLE! Found {len(found_values)} distinctive values")

                        # Try to verify with SOT records
                        verify_sot_records(cursor, full_table_name, found_values)

                except Exception as e:
                    print(f"   ❌ Error searching {table_name}: {e}")
                    continue

        connection.close()

    except Exception as e:
        print(f"❌ Search failed: {str(e)}")

def verify_sot_records(cursor, table_name, found_values):
    """Verify SOT records in the promising table"""
    print(f"\n🎯 VERIFYING SOT RECORDS IN: {table_name}")

    # SOT records for verification
    sot_records = [
        ("NO02", "5020", "10998", 23.0),
        ("NO02", "5020", "15500", 875.0),
        ("NO02", "5020", "7500323", 5000.0),
        ("NO02", "5020", "4309", 953.0),
        ("NO02", "5050", "92310", 3653.0),
        ("NO02", "5050", "10998", 310.0),
        ("DK01", "1020", "10998", 4145.0),
        ("DK01", "1020", "15500", 1461.0),
        ("DK01", "1050", "92310", 2762.0),
        ("DK01", "1050", "10998", 12203.0),
        ("DK01", "1050", "15500", 255.0)
    ]

    try:
        # Get table schema to identify columns
        cursor.execute(f"DESCRIBE {table_name}")
        columns = [row[0].lower() for row in cursor.fetchall()]

        # Try to identify key columns
        plant_col = None
        storage_col = None
        material_col = None

        for col in columns:
            if any(keyword in col for keyword in ['plant', 'werk']):
                plant_col = col
            elif any(keyword in col for keyword in ['storage', 'location', 'lgort']):
                storage_col = col
            elif any(keyword in col for keyword in ['material', 'matnr']):
                material_col = col

        # Get the most frequent column from found values (likely Ext Total)
        value_columns = [item[1] for item in found_values]
        ext_total_col = max(set(value_columns), key=value_columns.count)

        print(f"   Identified columns:")
        print(f"   Plant: {plant_col}")
        print(f"   Storage: {storage_col}")
        print(f"   Material: {material_col}")
        print(f"   Ext Total (likely): {ext_total_col}")

        if plant_col and material_col and ext_total_col:
            matches = 0
            for plant, storage, material, expected_value in sot_records:
                where_conditions = [f"{plant_col} = '{plant}'", f"{material_col} = '{material}'"]
                if storage_col:
                    where_conditions.append(f"{storage_col} = '{storage}'")

                where_clause = " AND ".join(where_conditions)
                query = f"SELECT {ext_total_col} FROM {table_name} WHERE {where_clause}"

                try:
                    cursor.execute(query)
                    result = cursor.fetchone()

                    if result:
                        db_value = result[0]
                        if abs(float(db_value) - float(expected_value)) < 0.01:
                            print(f"   ✅ {plant}|{storage}|{material}: {expected_value} = {db_value}")
                            matches += 1
                        else:
                            print(f"   ❌ {plant}|{storage}|{material}: {expected_value} ≠ {db_value}")
                    else:
                        print(f"   ❓ {plant}|{storage}|{material}: No record found")

                except Exception as e:
                    print(f"   ❌ Error checking {plant}|{storage}|{material}: {e}")

            match_rate = matches / len(sot_records)
            print(f"\n📊 VERIFICATION RESULT: {matches}/{len(sot_records)} matches ({match_rate:.1%})")

            if match_rate >= 0.7:
                print(f"🏆 FOUND THE EXT TOTAL TABLE!")
                print(f"   Table: {table_name}")
                print(f"   Ext Total Column: {ext_total_col}")
        else:
            print("❌ Could not identify required columns for verification")

    except Exception as e:
        print(f"❌ Error during verification: {e}")

if __name__ == "__main__":
    print("🔍 Searching for Ext Total in Databricks SAP Tables")

    # Test basic connection
    if test_databricks_connection():
        # List tables
        list_available_tables()

        # Search for Ext Total values
        search_for_ext_total()
    else:
        print("Cannot proceed - connection failed")

