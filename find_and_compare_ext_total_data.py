# Databricks notebook source
# MAGIC %md
# MAGIC # Find Ext Total Numbers and Compare with SOT
# MAGIC 
# MAGIC This notebook finds the exact Ext Total data in Databricks and compares it with your Excel SOT

# COMMAND ----------

import pandas as pd
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import numpy as np

# Initialize Spark session
spark = SparkSession.builder.appName("FindExtTotalNumbers").getOrCreate()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Load Excel SOT Data for Comparison

# COMMAND ----------

# First, let's load your Excel SOT data
# Update this path to where you uploaded your Excel file in Databricks
excel_file_path = "/FileStore/shared_uploads/your_username/Stock_Check_Nordics_15092025.XLSX"

# Try to load the Excel file
try:
    # Option 1: If you have the Excel file in DBFS
    sot_df = spark.read.format("com.crealytics.spark.excel") \
        .option("header", "true") \
        .option("inferSchema", "true") \
        .option("dataAddress", "Sheet1!A1") \
        .load(excel_file_path)
    
    print("✅ Loaded Excel SOT successfully")
    print(f"📊 SOT Records: {sot_df.count()}")
    
except Exception as e:
    print(f"❌ Could not load Excel file: {e}")
    print("📝 Please upload your Excel file to DBFS first")
    
    # Create sample SOT data based on what we saw earlier
    print("🔄 Using sample SOT data for demonstration...")
    
    sot_sample_data = [
        ("NO02", "8003", "Z001", "Obsolete-DIS VES", "09-15-25", "1:01:02 AM", "92310", "CORO EXTR KEG 30L REPAL NO", "PC", "GENERIC", "SAP", 14.0, 14.0, 0.0),
        ("NO02", "5020", "Z006", "Empties VES", "09-15-25", "1:01:02 AM", "10998", "PALLET OW IPPC 1000X1200", "PC", "", "SAP", 0.0, 23.0, 23.0),
        ("NO02", "5020", "Z005", "Empties VES", "09-15-25", "1:01:02 AM", "15500", "KEG CBW 30L SANKEY SAF IPPC + ST B 0300", "PC", "", "SAP", 500.0, 875.0, 375.0),
        ("NO02", "5020", "Z005", "Empties VES", "09-15-25", "1:01:02 AM", "34906", "PALLET EURO 800X1200 RET EPAL V3", "PC", "", "SAP", 133.0, 4.0, -129.0),
        ("NO02", "5050", "Z008", "POC Mat VES", "09-15-25", "1:01:02 AM", "7500323", "GLASS FRANZISKANER 50 CL", "PC", "", "SAP", 148.0, 310.0, 162.0),
        ("NO02", "5050", "Z008", "POC Mat VES", "09-15-25", "1:01:02 AM", "7567629", "HOEGAARDEN COASTER (1x100)", "PAC", "", "SAP", 0.0, 5000.0, 5000.0),
        ("DK01", "1020", "Z005", "Empties", "09-15-25", "2:00:46 AM", "4309", "KEG EURO SANKEY 30L BECK & CO", "PC", "", "SAP", 1461.0, 953.0, -508.0),
        ("DK01", "1020", "Z005", "Empties", "09-15-25", "2:00:46 AM", "15500", "KEG CBW 30L SANKEY SAF IPPC + ST B 0300", "PC", "", "SAP", 2762.0, 255.0, -2507.0),
        ("DK01", "1050", "Z008", "POC Materials", "09-15-25", "2:01:03 AM", "7574729", "LEFFE 27 x 75CL TRAY 1/4 FLAT DK", "PC", "", "SAP", 12203.0, 3653.0, -8550.0)
    ]
    
    sot_schema = StructType([
        StructField("Plant", StringType(), True),
        StructField("Storage_Location", StringType(), True),
        StructField("Material_Type", StringType(), True),
        StructField("Description_Storage_Location", StringType(), True),
        StructField("Date", StringType(), True),
        StructField("Time", StringType(), True),
        StructField("Material", StringType(), True),
        StructField("Material_Description", StringType(), True),
        StructField("UoM", StringType(), True),
        StructField("Batch", StringType(), True),
        StructField("System", StringType(), True),
        StructField("SAP_Total", DoubleType(), True),
        StructField("Ext_Total", DoubleType(), True),
        StructField("Total_Diff", DoubleType(), True)
    ])
    
    sot_df = spark.createDataFrame(sot_sample_data, sot_schema)

print("📋 SOT Data Sample:")
sot_df.show(5, truncate=False)

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Search for Databricks Tables with Matching Data

# COMMAND ----------

def find_matching_tables():
    """Find tables in Databricks that contain the same data as SOT"""
    
    # Get some key values from SOT to search for
    sot_sample = sot_df.limit(10).collect()
    
    search_values = []
    for row in sot_sample:
        search_values.extend([
            row.Plant,
            row.Storage_Location, 
            row.Material,
            str(row.SAP_Total),
            str(row.Ext_Total)
        ])
    
    # Remove duplicates and None values
    search_values = list(set([str(v) for v in search_values if v is not None]))
    
    print(f"🔍 Searching for these values in Databricks tables: {search_values[:10]}")
    
    # Get all databases
    databases = [row.databaseName for row in spark.sql("SHOW DATABASES").collect()]
    
    matching_tables = []
    
    for db_name in databases:
        try:
            tables = spark.sql(f"SHOW TABLES IN {db_name}").collect()
            
            for table_row in tables:
                table_name = table_row.tableName
                full_table_name = f"{db_name}.{table_name}"
                
                try:
                    # Get a sample of the table
                    sample_df = spark.sql(f"SELECT * FROM {full_table_name} LIMIT 100")
                    
                    # Convert to strings for searching
                    sample_data = []
                    for row in sample_df.collect():
                        for value in row.asDict().values():
                            if value is not None:
                                sample_data.append(str(value))
                    
                    # Check how many search values are found
                    matches = 0
                    found_values = []
                    
                    for search_val in search_values[:20]:  # Check first 20 values
                        if search_val in sample_data:
                            matches += 1
                            found_values.append(search_val)
                    
                    if matches >= 3:  # At least 3 matching values
                        matching_tables.append({
                            'table': full_table_name,
                            'matches': matches,
                            'found_values': found_values[:5],  # First 5 matches
                            'total_rows': sample_df.count()
                        })
                        
                        print(f"🎯 Found matching table: {full_table_name}")
                        print(f"   Matches: {matches}, Values: {found_values[:3]}")
                
                except Exception as e:
                    continue
                    
        except Exception as e:
            continue
    
    return matching_tables

# Find tables with matching data
print("🔍 Searching for tables with matching data...")
matching_tables = find_matching_tables()

print(f"\n📊 Found {len(matching_tables)} tables with matching data")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Detailed Analysis of Matching Tables

# COMMAND ----------

def analyze_matching_table(table_info):
    """Analyze a specific table to see if it contains our Ext Total data"""
    
    table_name = table_info['table']
    print(f"\n🔍 ANALYZING: {table_name}")
    print("="*60)
    
    try:
        # Get the table
        df = spark.sql(f"SELECT * FROM {table_name}")
        
        # Show schema
        print("📋 SCHEMA:")
        df.printSchema()
        
        # Show sample data
        print("\n📄 SAMPLE DATA:")
        df.show(5, truncate=False)
        
        # Look for columns that might be Ext Total
        columns = df.columns
        ext_total_candidates = []
        
        for col in columns:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in ['ext', 'external', 'total', 'physical']):
                ext_total_candidates.append(col)
        
        print(f"\n🎯 Potential Ext Total columns: {ext_total_candidates}")
        
        # Check for Nordic plant codes
        plant_columns = [col for col in columns if 'plant' in col.lower()]
        if plant_columns:
            plant_values = df.select(plant_columns[0]).distinct().collect()
            plant_list = [row[0] for row in plant_values if row[0]]
            print(f"🏭 Plant values found: {plant_list}")
            
            # Check if we have Nordic plants
            nordic_plants = [p for p in plant_list if p in ['NO02', 'DK01']]
            if nordic_plants:
                print(f"🎯 NORDIC PLANTS FOUND: {nordic_plants}")
                return True, ext_total_candidates
        
        # Check for specific material numbers from SOT
        material_columns = [col for col in columns if 'material' in col.lower() and 'description' not in col.lower()]
        if material_columns:
            # Check for specific materials from our SOT
            sot_materials = ['92310', '10998', '15500', '34906', '7500323', '4309']
            
            for material in sot_materials:
                count = df.filter(col(material_columns[0]) == material).count()
                if count > 0:
                    print(f"🎯 Found SOT material {material}: {count} records")
                    return True, ext_total_candidates
        
        return False, ext_total_candidates
        
    except Exception as e:
        print(f"❌ Error analyzing {table_name}: {e}")
        return False, []

# Analyze each matching table
promising_tables = []

for table_info in matching_tables:
    is_promising, ext_cols = analyze_matching_table(table_info)
    if is_promising:
        promising_tables.append({
            'table': table_info['table'],
            'ext_columns': ext_cols,
            'matches': table_info['matches']
        })

print(f"\n🏆 PROMISING TABLES: {len(promising_tables)}")
for table in promising_tables:
    print(f"  - {table['table']}: {table['ext_columns']}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Extract and Compare Ext Total Numbers

# COMMAND ----------

def compare_with_sot(databricks_table, ext_total_column):
    """Compare Databricks table data with SOT"""
    
    print(f"\n🔍 COMPARING: {databricks_table}")
    print("="*60)
    
    try:
        # Load Databricks data
        db_df = spark.sql(f"SELECT * FROM {databricks_table}")
        
        print(f"📊 Databricks table rows: {db_df.count()}")
        print(f"📊 SOT rows: {sot_df.count()}")
        
        # Try to match records based on Plant, Storage Location, and Material
        # First, let's see what columns we have
        db_columns = db_df.columns
        sot_columns = sot_df.columns
        
        print(f"\n📋 Databricks columns: {db_columns}")
        print(f"📋 SOT columns: {sot_columns}")
        
        # Find matching column names (case insensitive)
        column_mapping = {}
        
        for sot_col in ['Plant', 'Storage_Location', 'Material', 'SAP_Total', 'Ext_Total']:
            for db_col in db_columns:
                if sot_col.lower().replace('_', '') in db_col.lower().replace('_', ''):
                    column_mapping[sot_col] = db_col
                    break
        
        print(f"\n🔗 Column mapping: {column_mapping}")
        
        if len(column_mapping) >= 3:  # Need at least 3 matching columns
            
            # Create comparison dataset
            if 'Plant' in column_mapping and 'Material' in column_mapping:
                
                # Get specific records for comparison
                test_materials = ['92310', '10998', '15500', '7500323', '4309']
                
                print(f"\n🔍 Checking specific materials: {test_materials}")
                
                for material in test_materials:
                    print(f"\n📋 Material: {material}")
                    
                    # SOT data for this material
                    sot_material = sot_df.filter(col("Material") == material)
                    sot_count = sot_material.count()
                    
                    if sot_count > 0:
                        print(f"  SOT records: {sot_count}")
                        sot_material.select("Plant", "Storage_Location", "Material", "SAP_Total", "Ext_Total").show()
                        
                        # Databricks data for this material
                        db_material = db_df.filter(col(column_mapping['Material']) == material)
                        db_count = db_material.count()
                        
                        if db_count > 0:
                            print(f"  Databricks records: {db_count}")
                            
                            # Show the comparison
                            if ext_total_column in db_columns:
                                comparison_cols = [column_mapping.get('Plant', 'Plant'), 
                                                 column_mapping.get('Storage_Location', 'Storage_Location'),
                                                 column_mapping.get('Material', 'Material'),
                                                 column_mapping.get('SAP_Total', 'SAP_Total'),
                                                 ext_total_column]
                                
                                db_material.select(*[col for col in comparison_cols if col in db_columns]).show()
                                
                                # Extract specific values for comparison
                                sot_values = sot_material.collect()
                                db_values = db_material.collect()
                                
                                print(f"  🔍 DETAILED COMPARISON:")
                                for sot_row in sot_values:
                                    sot_ext = sot_row.Ext_Total
                                    sot_sap = sot_row.SAP_Total
                                    sot_plant = sot_row.Plant
                                    sot_storage = sot_row.Storage_Location
                                    
                                    # Find matching record in Databricks
                                    matching_db = None
                                    for db_row in db_values:
                                        db_dict = db_row.asDict()
                                        if (db_dict.get(column_mapping.get('Plant')) == sot_plant and 
                                            db_dict.get(column_mapping.get('Storage_Location')) == sot_storage):
                                            matching_db = db_row
                                            break
                                    
                                    if matching_db:
                                        db_dict = matching_db.asDict()
                                        db_ext = db_dict.get(ext_total_column)
                                        db_sap = db_dict.get(column_mapping.get('SAP_Total'))
                                        
                                        print(f"    Plant {sot_plant}, Storage {sot_storage}:")
                                        print(f"      SOT - SAP: {sot_sap}, Ext: {sot_ext}")
                                        print(f"      DB  - SAP: {db_sap}, Ext: {db_ext}")
                                        
                                        if sot_ext != db_ext:
                                            print(f"      ❌ MISMATCH! Ext Total differs by {abs(sot_ext - db_ext) if db_ext else 'N/A'}")
                                        else:
                                            print(f"      ✅ MATCH!")
                            else:
                                print(f"  ❌ Ext Total column '{ext_total_column}' not found")
                        else:
                            print(f"  ❌ Material {material} not found in Databricks table")
                    else:
                        print(f"  ❌ Material {material} not found in SOT")
        else:
            print("❌ Not enough matching columns to perform comparison")
            
    except Exception as e:
        print(f"❌ Error comparing data: {e}")

# Compare with the most promising tables
if promising_tables:
    for table_info in promising_tables:
        table_name = table_info['table']
        ext_columns = table_info['ext_columns']
        
        if ext_columns:
            # Use the first ext column found
            ext_col = ext_columns[0]
            compare_with_sot(table_name, ext_col)
        else:
            print(f"⚠️ No Ext Total column found in {table_name}")
else:
    print("❌ No promising tables found. The data might be in a different format or location.")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Summary and Findings

# COMMAND ----------

print("🎯 SEARCH AND COMPARISON SUMMARY")
print("="*60)

print(f"\n📊 SOT Data Summary:")
print(f"  - Total records: {sot_df.count()}")
print(f"  - Plants: {[row.Plant for row in sot_df.select('Plant').distinct().collect()]}")
print(f"  - Storage locations: {sot_df.select('Storage_Location').distinct().count()}")

total_sot_ext = sot_df.agg(sum("Ext_Total").alias("total")).collect()[0].total
total_sot_sap = sot_df.agg(sum("SAP_Total").alias("total")).collect()[0].total
print(f"  - Total SAP: {total_sot_sap:,.0f}")
print(f"  - Total Ext: {total_sot_ext:,.0f}")
print(f"  - Total Diff: {total_sot_ext - total_sot_sap:,.0f}")

print(f"\n🔍 Databricks Search Results:")
print(f"  - Tables searched: Multiple databases")
print(f"  - Matching tables found: {len(matching_tables)}")
print(f"  - Promising tables: {len(promising_tables)}")

if promising_tables:
    print(f"\n🏆 MOST LIKELY TABLES:")
    for i, table in enumerate(promising_tables, 1):
        print(f"  {i}. {table['table']}")
        print(f"     Ext columns: {table['ext_columns']}")
        print(f"     Match score: {table['matches']}")

print(f"\n💡 NEXT STEPS:")
print("1. Check the promising tables listed above")
print("2. Verify the Ext Total column names match your expectations")
print("3. Run detailed comparisons on the most likely candidates")
print("4. If no matches found, check:")
print("   - Different database schemas")
print("   - Tables with different naming conventions")
print("   - Recently uploaded data that might not be indexed yet")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 6. Manual Check Functions

# COMMAND ----------

def quick_check_specific_table(table_name, ext_column_name):
    """Quick function to check a specific table you suspect contains the data"""
    
    print(f"🔍 QUICK CHECK: {table_name}")
    print("="*50)
    
    try:
        df = spark.sql(f"SELECT * FROM {table_name}")
        
        print(f"📊 Total rows: {df.count()}")
        print(f"📋 Columns: {df.columns}")
        
        # Check for Nordic plants
        if 'plant' in [col.lower() for col in df.columns]:
            plant_col = [col for col in df.columns if 'plant' in col.lower()][0]
            plants = [row[0] for row in df.select(plant_col).distinct().collect()]
            print(f"🏭 Plants: {plants}")
        
        # Show sample with Ext Total column
        if ext_column_name in df.columns:
            print(f"\n📄 Sample data with {ext_column_name}:")
            df.select("*").show(5, truncate=False)
            
            # Quick stats on Ext Total
            ext_stats = df.agg(
                sum(ext_column_name).alias("total_ext"),
                count(ext_column_name).alias("count_ext"),
                avg(ext_column_name).alias("avg_ext")
            ).collect()[0]
            
            print(f"📊 {ext_column_name} Stats:")
            print(f"  - Total: {ext_stats.total_ext:,.0f}")
            print(f"  - Count: {ext_stats.count_ext:,}")
            print(f"  - Average: {ext_stats.avg_ext:,.2f}")
            
        else:
            print(f"❌ Column '{ext_column_name}' not found")
            print(f"Available columns: {df.columns}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

# Example usage:
# quick_check_specific_table("your_database.your_table", "ext_total")

print("✅ ANALYSIS COMPLETE!")
print("\nUse quick_check_specific_table('table_name', 'ext_column') to check specific tables")
print("The most promising tables are listed above - start with those!")
