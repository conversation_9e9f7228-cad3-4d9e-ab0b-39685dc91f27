# Databricks notebook source
# MAGIC %md
# MAGIC # Find Ext Total Data in Databricks
# MAGIC 
# MAGIC This notebook searches through all databases and tables to find where the "Ext Total" data is stored.

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Search All Databases and Tables

# COMMAND ----------

import pandas as pd
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
import re

# Initialize Spark session
spark = SparkSession.builder.appName("FindExtTotalData").getOrCreate()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Get All Databases

# COMMAND ----------

# Get all databases
databases = spark.sql("SHOW DATABASES").collect()
database_names = [row.databaseName for row in databases]

print("Available databases:")
for db in database_names:
    print(f"  - {db}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Search for Tables with Potential Ext Total Data

# COMMAND ----------

def search_tables_for_ext_total():
    """Search all tables for columns that might contain Ext Total data"""
    
    potential_tables = []
    
    for db_name in database_names:
        try:
            print(f"\n🔍 Searching database: {db_name}")
            
            # Get all tables in this database
            tables = spark.sql(f"SHOW TABLES IN {db_name}").collect()
            
            for table_row in tables:
                table_name = table_row.tableName
                full_table_name = f"{db_name}.{table_name}"
                
                try:
                    # Get table schema
                    columns = spark.sql(f"DESCRIBE {full_table_name}").collect()
                    column_names = [row.col_name.lower() for row in columns if row.col_name]
                    
                    # Search for potential Ext Total columns
                    ext_total_columns = []
                    for col in column_names:
                        if any(keyword in col for keyword in ['ext', 'external', 'total', 'physical', 'count', 'inventory']):
                            ext_total_columns.append(col)
                    
                    # Also check for Nordic/Norway/Denmark related tables
                    table_keywords = ['nordic', 'norway', 'denmark', 'inventory', 'stock', 'material', 'warehouse']
                    has_relevant_keywords = any(keyword in table_name.lower() for keyword in table_keywords)
                    
                    if ext_total_columns or has_relevant_keywords:
                        potential_tables.append({
                            'database': db_name,
                            'table': table_name,
                            'full_name': full_table_name,
                            'ext_columns': ext_total_columns,
                            'all_columns': column_names,
                            'has_keywords': has_relevant_keywords
                        })
                        
                        print(f"  ✅ Found potential table: {full_table_name}")
                        if ext_total_columns:
                            print(f"     Ext-related columns: {ext_total_columns}")
                
                except Exception as e:
                    print(f"  ❌ Error accessing {full_table_name}: {str(e)}")
                    continue
                    
        except Exception as e:
            print(f"❌ Error accessing database {db_name}: {str(e)}")
            continue
    
    return potential_tables

# Run the search
potential_tables = search_tables_for_ext_total()

print(f"\n📊 Found {len(potential_tables)} potential tables")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Detailed Analysis of Potential Tables

# COMMAND ----------

def analyze_potential_tables(tables):
    """Analyze each potential table in detail"""
    
    results = []
    
    for table_info in tables:
        full_name = table_info['full_name']
        print(f"\n🔍 Analyzing: {full_name}")
        
        try:
            # Get row count
            row_count = spark.sql(f"SELECT COUNT(*) as count FROM {full_name}").collect()[0].count
            
            # Get sample data
            sample_df = spark.sql(f"SELECT * FROM {full_name} LIMIT 5")
            
            # Check for specific patterns in the data
            columns = sample_df.columns
            
            # Look for columns that might be Ext Total
            ext_total_candidates = []
            for col in columns:
                col_lower = col.lower()
                if any(keyword in col_lower for keyword in ['ext', 'external', 'physical']):
                    ext_total_candidates.append(col)
            
            # Look for Nordic-specific data patterns
            sample_data = sample_df.collect()
            has_nordic_data = False
            
            for row in sample_data:
                row_dict = row.asDict()
                for key, value in row_dict.items():
                    if value and isinstance(value, str):
                        if any(pattern in value.upper() for pattern in ['NO02', 'DK01', 'NORDIC', 'NORWAY', 'DENMARK']):
                            has_nordic_data = True
                            break
                if has_nordic_data:
                    break
            
            result = {
                'table': full_name,
                'row_count': row_count,
                'columns': columns,
                'ext_candidates': ext_total_candidates,
                'has_nordic_data': has_nordic_data,
                'sample_data': sample_data[:2]  # First 2 rows
            }
            
            results.append(result)
            
            print(f"  📊 Rows: {row_count:,}")
            print(f"  📋 Columns: {len(columns)}")
            print(f"  🎯 Ext candidates: {ext_total_candidates}")
            print(f"  🌍 Nordic data: {has_nordic_data}")
            
            # Show sample if it looks promising
            if ext_total_candidates or has_nordic_data or row_count > 1000:
                print(f"  📄 Sample data:")
                sample_df.show(2, truncate=False)
            
        except Exception as e:
            print(f"  ❌ Error analyzing {full_name}: {str(e)}")
            continue
    
    return results

# Analyze the potential tables
analysis_results = analyze_potential_tables(potential_tables)

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Search by Column Names Specifically

# COMMAND ----------

def search_by_column_patterns():
    """Search specifically for tables with columns matching our Excel structure"""
    
    target_columns = [
        'ext_total', 'ext total', 'external_total', 'external total',
        'sap_total', 'sap total', 'total_diff', 'total diff',
        'plant', 'storage_location', 'storage location',
        'material', 'material_description', 'material description'
    ]
    
    matching_tables = []
    
    for db_name in database_names:
        try:
            tables = spark.sql(f"SHOW TABLES IN {db_name}").collect()
            
            for table_row in tables:
                table_name = table_row.tableName
                full_table_name = f"{db_name}.{table_name}"
                
                try:
                    # Get columns
                    columns = spark.sql(f"DESCRIBE {full_table_name}").collect()
                    column_names = [row.col_name.lower().replace(' ', '_') for row in columns if row.col_name]
                    
                    # Count matches
                    matches = []
                    for target in target_columns:
                        target_clean = target.lower().replace(' ', '_')
                        if target_clean in column_names:
                            matches.append(target)
                    
                    if len(matches) >= 3:  # At least 3 matching columns
                        matching_tables.append({
                            'table': full_table_name,
                            'matches': matches,
                            'match_count': len(matches),
                            'all_columns': column_names
                        })
                        
                        print(f"🎯 Strong match: {full_table_name}")
                        print(f"   Matches: {matches}")
                
                except Exception as e:
                    continue
                    
        except Exception as e:
            continue
    
    return matching_tables

# Search by column patterns
column_matches = search_by_column_patterns()

print(f"\n🎯 Found {len(column_matches)} tables with strong column matches")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 6. Search Recent Tables (Last 30 Days)

# COMMAND ----------

def find_recent_tables():
    """Find tables created or modified in the last 30 days"""
    
    recent_tables = []
    
    for db_name in database_names:
        try:
            # Get table details
            tables_info = spark.sql(f"""
                SHOW TABLE EXTENDED IN {db_name} LIKE '*'
            """).collect()
            
            for table_info in tables_info:
                # This might not work in all Databricks versions
                # Alternative approach below
                pass
                
        except Exception as e:
            # Alternative: just list tables and check manually
            try:
                tables = spark.sql(f"SHOW TABLES IN {db_name}").collect()
                for table_row in tables:
                    table_name = table_row.tableName
                    full_table_name = f"{db_name}.{table_name}"
                    
                    # Check if table name suggests recent upload
                    if any(pattern in table_name.lower() for pattern in ['2024', '2025', 'recent', 'upload', 'import']):
                        recent_tables.append(full_table_name)
                        print(f"📅 Recent table candidate: {full_table_name}")
                        
            except Exception as e2:
                continue
    
    return recent_tables

recent_tables = find_recent_tables()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 7. Search by Data Patterns

# COMMAND ----------

def search_by_data_patterns():
    """Search for tables containing the specific data patterns from your Excel"""
    
    # Known values from your Excel
    search_patterns = [
        "NO02",  # Plant
        "DK01",  # Plant
        "8003",  # Storage Location
        "5020",  # Storage Location
        "5050",  # Storage Location
        "Z001",  # Material Type
        "Z005",  # Material Type
        "92310", # Material
        "CORO EXTR KEG",  # Material Description
        "Empties VES",    # Storage Description
        "POC Mat VES"     # Storage Description
    ]
    
    pattern_matches = []
    
    for table_info in potential_tables:
        full_name = table_info['full_name']
        
        try:
            # Sample more data for pattern matching
            sample_df = spark.sql(f"SELECT * FROM {full_name} LIMIT 100")
            
            pattern_count = 0
            found_patterns = []
            
            # Check each pattern
            for pattern in search_patterns:
                try:
                    # Search across all string columns
                    string_columns = [col for col, dtype in sample_df.dtypes if dtype == 'string']
                    
                    for col_name in string_columns:
                        count = sample_df.filter(col(col_name).contains(pattern)).count()
                        if count > 0:
                            pattern_count += 1
                            found_patterns.append(f"{pattern} in {col_name}")
                            break
                            
                except Exception as e:
                    continue
            
            if pattern_count >= 3:  # Found at least 3 patterns
                pattern_matches.append({
                    'table': full_name,
                    'pattern_count': pattern_count,
                    'found_patterns': found_patterns
                })
                
                print(f"🔍 Pattern match: {full_name}")
                print(f"   Found {pattern_count} patterns: {found_patterns[:5]}")
                
        except Exception as e:
            continue
    
    return pattern_matches

# Search by data patterns
pattern_matches = search_by_data_patterns()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 8. Summary and Recommendations

# COMMAND ----------

print("="*60)
print("🎯 SEARCH RESULTS SUMMARY")
print("="*60)

print(f"\n📊 Total databases searched: {len(database_names)}")
print(f"🔍 Potential tables found: {len(potential_tables)}")
print(f"🎯 Strong column matches: {len(column_matches)}")
print(f"📅 Recent tables: {len(recent_tables)}")
print(f"🔍 Pattern matches: {len(pattern_matches)}")

print("\n🏆 TOP CANDIDATES:")
print("-" * 40)

# Rank tables by likelihood
all_candidates = {}

# Add points for different criteria
for table in potential_tables:
    name = table['full_name']
    all_candidates[name] = all_candidates.get(name, 0) + 1

for table in column_matches:
    name = table['table']
    all_candidates[name] = all_candidates.get(name, 0) + 3  # Higher weight

for table in pattern_matches:
    name = table['table']
    all_candidates[name] = all_candidates.get(name, 0) + 2

# Sort by score
sorted_candidates = sorted(all_candidates.items(), key=lambda x: x[1], reverse=True)

for i, (table_name, score) in enumerate(sorted_candidates[:10], 1):
    print(f"{i}. {table_name} (Score: {score})")

print("\n💡 NEXT STEPS:")
print("-" * 40)
print("1. Check the top-ranked tables above")
print("2. Look for tables with 'inventory', 'stock', or 'material' in the name")
print("3. Check tables in databases related to your business unit")
print("4. Look for recently created tables (last few days/weeks)")
print("5. Ask your data team about Nordic inventory data uploads")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 9. Quick Check Commands

# COMMAND ----------

print("🔧 QUICK CHECK COMMANDS:")
print("-" * 40)
print("Run these commands to quickly check specific tables:")
print()

for i, (table_name, score) in enumerate(sorted_candidates[:5], 1):
    print(f"# Check candidate {i}:")
    print(f"spark.sql('SELECT * FROM {table_name} LIMIT 10').show()")
    print(f"spark.sql('DESCRIBE {table_name}').show()")
    print()

print("# Search for tables with 'ext' in column names:")
print("for db in ['default', 'your_main_db']:  # Replace with your main databases")
print("    spark.sql(f'SHOW TABLES IN {db}').show()")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 10. Manual Search Helpers

# COMMAND ----------

# Function to quickly check a specific table
def quick_check_table(table_name):
    """Quickly check if a table contains Ext Total data"""
    try:
        print(f"🔍 Checking: {table_name}")
        
        # Get schema
        schema = spark.sql(f"DESCRIBE {table_name}").collect()
        columns = [row.col_name for row in schema if row.col_name]
        
        print(f"📋 Columns ({len(columns)}): {columns}")
        
        # Get sample data
        sample = spark.sql(f"SELECT * FROM {table_name} LIMIT 3")
        print(f"📄 Sample data:")
        sample.show(truncate=False)
        
        # Check for Nordic patterns
        row_count = spark.sql(f"SELECT COUNT(*) as count FROM {table_name}").collect()[0].count
        print(f"📊 Total rows: {row_count:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

# Example usage:
# quick_check_table("your_database.your_table_name")

print("✅ Search complete! Use the functions above to investigate specific tables.")
