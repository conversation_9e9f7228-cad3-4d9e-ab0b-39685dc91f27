# Databricks notebook source
# MAGIC %md
# MAGIC # Quick Ext Total Table Finder
# MAGIC 
# MAGIC Fast search to find your Ext Total data in Databricks

# COMMAND ----------

# Quick search for Ext Total data
import pyspark.sql.functions as F

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. List All Your Databases

# COMMAND ----------

# Show all databases
print("🗄️ Available Databases:")
spark.sql("SHOW DATABASES").show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Search Common Database Names

# COMMAND ----------

# Common database names to check first
common_dbs = [
    'default',
    'inventory', 
    'warehouse',
    'sap',
    'nordics',
    'materials',
    'stock',
    'supply_chain',
    'operations',
    'finance'
]

print("🔍 Checking common database names...")
existing_dbs = []

for db in common_dbs:
    try:
        tables = spark.sql(f"SHOW TABLES IN {db}")
        existing_dbs.append(db)
        print(f"✅ Found database: {db}")
    except:
        pass

print(f"\n📋 Existing databases to search: {existing_dbs}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Quick Table Search by Keywords

# COMMAND ----------

def find_tables_with_keywords(keywords):
    """Find tables containing specific keywords"""
    
    all_dbs = [row.databaseName for row in spark.sql("SHOW DATABASES").collect()]
    matching_tables = []
    
    for db in all_dbs:
        try:
            tables = spark.sql(f"SHOW TABLES IN {db}").collect()
            
            for table_row in tables:
                table_name = table_row.tableName.lower()
                
                # Check if any keyword is in table name
                for keyword in keywords:
                    if keyword.lower() in table_name:
                        full_name = f"{db}.{table_row.tableName}"
                        matching_tables.append(full_name)
                        print(f"🎯 Found: {full_name}")
                        break
                        
        except Exception as e:
            continue
    
    return matching_tables

# Search for tables with relevant keywords
keywords = [
    'inventory', 'stock', 'material', 'warehouse', 'nordic', 
    'norway', 'denmark', 'ext', 'external', 'total', 'sap'
]

print("🔍 Searching for tables with relevant keywords...")
keyword_tables = find_tables_with_keywords(keywords)

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Search for Columns with "Ext" or "Total"

# COMMAND ----------

def find_tables_with_ext_columns():
    """Find tables with columns containing 'ext' or 'total'"""
    
    all_dbs = [row.databaseName for row in spark.sql("SHOW DATABASES").collect()]
    ext_tables = []
    
    for db in all_dbs:
        try:
            tables = spark.sql(f"SHOW TABLES IN {db}").collect()
            
            for table_row in tables:
                table_name = table_row.tableName
                full_name = f"{db}.{table_name}"
                
                try:
                    # Get column names
                    columns = spark.sql(f"DESCRIBE {full_name}").collect()
                    column_names = [row.col_name.lower() for row in columns if row.col_name]
                    
                    # Check for ext/total columns
                    ext_cols = [col for col in column_names if 'ext' in col or 'total' in col]
                    
                    if ext_cols:
                        ext_tables.append({
                            'table': full_name,
                            'ext_columns': ext_cols
                        })
                        print(f"📊 {full_name}: {ext_cols}")
                        
                except Exception as e:
                    continue
                    
        except Exception as e:
            continue
    
    return ext_tables

print("🔍 Searching for tables with 'ext' or 'total' columns...")
ext_column_tables = find_tables_with_ext_columns()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Check for Nordic-Specific Data

# COMMAND ----------

def check_for_nordic_data(table_list):
    """Check tables for Nordic-specific data patterns"""
    
    nordic_patterns = ['NO02', 'DK01', 'NORDIC', 'NORWAY', 'DENMARK']
    nordic_tables = []
    
    for table_info in table_list:
        if isinstance(table_info, dict):
            table_name = table_info['table']
        else:
            table_name = table_info
            
        try:
            print(f"🔍 Checking {table_name} for Nordic data...")
            
            # Get sample data
            sample = spark.sql(f"SELECT * FROM {table_name} LIMIT 10")
            
            # Check for Nordic patterns in the data
            found_patterns = []
            for pattern in nordic_patterns:
                try:
                    # Check all string columns for the pattern
                    string_cols = [col for col, dtype in sample.dtypes if dtype == 'string']
                    
                    for col_name in string_cols:
                        count = sample.filter(F.col(col_name).contains(pattern)).count()
                        if count > 0:
                            found_patterns.append(f"{pattern} in {col_name}")
                            break
                            
                except Exception as e:
                    continue
            
            if found_patterns:
                nordic_tables.append({
                    'table': table_name,
                    'patterns': found_patterns
                })
                print(f"🎯 NORDIC DATA FOUND in {table_name}: {found_patterns}")
                
                # Show sample of this promising table
                print("📄 Sample data:")
                sample.show(5, truncate=False)
                
        except Exception as e:
            print(f"❌ Error checking {table_name}: {e}")
            continue
    
    return nordic_tables

# Check the most promising tables for Nordic data
all_candidate_tables = keyword_tables + [t['table'] for t in ext_column_tables]
unique_tables = list(set(all_candidate_tables))

print(f"🔍 Checking {len(unique_tables)} candidate tables for Nordic data...")
nordic_data_tables = check_for_nordic_data(unique_tables)

# COMMAND ----------

# MAGIC %md
# MAGIC ## 6. Quick Manual Checks

# COMMAND ----------

# Function for quick manual table inspection
def inspect_table(table_name):
    """Quick inspection of a specific table"""
    try:
        print(f"🔍 INSPECTING: {table_name}")
        print("="*50)
        
        # Schema
        print("📋 COLUMNS:")
        spark.sql(f"DESCRIBE {table_name}").show()
        
        # Row count
        count = spark.sql(f"SELECT COUNT(*) as count FROM {table_name}").collect()[0].count
        print(f"📊 TOTAL ROWS: {count:,}")
        
        # Sample data
        print("📄 SAMPLE DATA:")
        spark.sql(f"SELECT * FROM {table_name} LIMIT 5").show(truncate=False)
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

# COMMAND ----------

# MAGIC %md
# MAGIC ## 7. Results Summary

# COMMAND ----------

print("🎯 SEARCH RESULTS SUMMARY")
print("="*50)

print(f"\n📊 Tables with relevant keywords: {len(keyword_tables)}")
for table in keyword_tables[:5]:  # Show top 5
    print(f"  - {table}")

print(f"\n📊 Tables with ext/total columns: {len(ext_column_tables)}")
for table in ext_column_tables[:5]:  # Show top 5
    print(f"  - {table['table']}: {table['ext_columns']}")

print(f"\n🎯 Tables with Nordic data: {len(nordic_data_tables)}")
for table in nordic_data_tables:
    print(f"  - {table['table']}: {table['patterns']}")

print("\n💡 MOST LIKELY CANDIDATES:")
print("-"*30)

# Rank by likelihood
if nordic_data_tables:
    print("🏆 TOP PRIORITY - Tables with Nordic data:")
    for i, table in enumerate(nordic_data_tables, 1):
        print(f"  {i}. {table['table']}")
        
elif ext_column_tables:
    print("🥈 SECOND PRIORITY - Tables with ext/total columns:")
    for i, table in enumerate(ext_column_tables[:3], 1):
        print(f"  {i}. {table['table']}")
        
elif keyword_tables:
    print("🥉 THIRD PRIORITY - Tables with relevant keywords:")
    for i, table in enumerate(keyword_tables[:3], 1):
        print(f"  {i}. {table}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 8. Quick Check Commands

# COMMAND ----------

print("🔧 QUICK CHECK COMMANDS:")
print("Copy and run these to inspect the most promising tables:")
print()

if nordic_data_tables:
    for i, table in enumerate(nordic_data_tables[:3], 1):
        table_name = table['table']
        print(f"# Check Nordic table {i}:")
        print(f"inspect_table('{table_name}')")
        print()

elif ext_column_tables:
    for i, table in enumerate(ext_column_tables[:3], 1):
        table_name = table['table']
        print(f"# Check ext/total table {i}:")
        print(f"inspect_table('{table_name}')")
        print()

print("# Or manually check any table:")
print("inspect_table('database_name.table_name')")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 9. Search by Upload Date (if available)

# COMMAND ----------

# Try to find recently uploaded tables
print("🔍 Looking for recently uploaded tables...")

try:
    # This might work in some Databricks environments
    recent_tables = spark.sql("""
        SHOW TABLES 
    """).collect()
    
    print("📅 All tables (check manually for recent uploads):")
    for table in recent_tables:
        print(f"  - {table.database}.{table.tableName}")
        
except Exception as e:
    print("ℹ️ Cannot automatically detect upload dates. Check with your data team.")

# COMMAND ----------

print("✅ SEARCH COMPLETE!")
print("\n🎯 NEXT STEPS:")
print("1. Check the Nordic data tables first (highest priority)")
print("2. Use inspect_table() function on promising candidates")
print("3. Look for tables uploaded around the same time as your Excel file")
print("4. Ask your data team about Nordic inventory uploads")
print("5. Check file upload logs or job histories in Databricks")
