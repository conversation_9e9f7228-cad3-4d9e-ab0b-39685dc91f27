#!/usr/bin/env python3
"""
Focused Ext Total Search - Target specific databases likely to contain inventory data
"""

import os
from dotenv import load_dotenv
from databricks import sql

# Load environment variables
load_dotenv()

class FocusedExtTotalSearch:
    def __init__(self):
        self.connection = None
        self.cursor = None
        
        # Our distinctive values from the Excel SOT
        self.distinctive_values = [23.0, 875.0, 5000.0, 953.0, 3653.0, 310.0, 4145.0, 1461.0, 2762.0, 12203.0, 255.0]
        
        # SOT records for exact matching
        self.sot_records = [
            ("NO02", "5020", "10998", 0.0, 23.0, 23.0),
            ("NO02", "5020", "15500", 500.0, 875.0, 375.0),
            ("NO02", "5020", "34906", 133.0, 4.0, -129.0),
            ("NO02", "5020", "7500323", 5000.0, 5000.0, 0.0),
            ("NO02", "5020", "4309", 953.0, 953.0, 0.0),
            ("NO02", "5050", "92310", 3653.0, 3653.0, 0.0),
            ("NO02", "5050", "10998", 310.0, 310.0, 0.0),
            ("DK01", "1020", "10998", 4145.0, 4145.0, 0.0),
            ("DK01", "1020", "15500", 1461.0, 1461.0, 0.0),
            ("DK01", "1050", "92310", 2762.0, 2762.0, 0.0),
            ("DK01", "1050", "10998", 12203.0, 12203.0, 0.0),
            ("DK01", "1050", "15500", 255.0, 255.0, 0.0)
        ]
        
        # Target databases most likely to contain inventory data
        self.target_databases = [
            'brewdat_uc_europe_prod',
            'slv_eur_tech_sap_ecc_europe',
            'brewdat_uc_europe',
            'sap_ecc_europe',
            'inventory_data',
            'stock_data',
            'material_data',
            'plant_data'
        ]
        
    def connect(self):
        """Connect to Databricks"""
        try:
            self.connection = sql.connect(
                server_hostname=os.getenv('DATABRICKS_HOST'),
                http_path=os.getenv('DATABRICKS_HTTP_PATH'),
                access_token=os.getenv('DATABRICKS_TOKEN')
            )
            self.cursor = self.connection.cursor()
            print("✅ Connected to Databricks successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to Databricks: {e}")
            return False
    
    def search_specific_database(self, database_name):
        """Search a specific database for inventory tables"""
        print(f"\n🔍 Searching database: {database_name}")
        
        try:
            # Check if database exists
            self.cursor.execute("SHOW DATABASES")
            available_dbs = [row[0] for row in self.cursor.fetchall()]
            
            if database_name not in available_dbs:
                print(f"   ❌ Database {database_name} not found")
                return []
            
            # Get tables in this database
            self.cursor.execute(f"SHOW TABLES IN {database_name}")
            tables = self.cursor.fetchall()
            
            print(f"   📊 Found {len(tables)} tables")
            
            promising_tables = []
            
            for table_row in tables:
                if len(table_row) >= 2:
                    table_name = table_row[1]  # table name is usually in second column
                    full_table_name = f"{database_name}.{table_name}"
                    
                    # Skip obviously irrelevant tables
                    skip_keywords = ['log', 'temp', 'test', 'backup', '_err', 'audit']
                    if any(skip in table_name.lower() for skip in skip_keywords):
                        continue
                    
                    # Look for inventory-related table names
                    inventory_keywords = ['stock', 'inventory', 'material', 'plant', 'storage', 'mard', 'mbew']
                    if any(keyword in table_name.lower() for keyword in inventory_keywords):
                        print(f"   🎯 Found inventory-related table: {table_name}")
                        result = self.analyze_table(full_table_name)
                        if result:
                            promising_tables.append(result)
            
            return promising_tables
            
        except Exception as e:
            print(f"   ❌ Error searching database {database_name}: {e}")
            return []
    
    def analyze_table(self, table_name):
        """Analyze a table for our distinctive values"""
        print(f"      🔍 Analyzing {table_name}...")
        
        try:
            # Get table schema
            self.cursor.execute(f"DESCRIBE {table_name}")
            columns = self.cursor.fetchall()
            
            # Look for numeric columns that might contain our values
            numeric_columns = []
            for col in columns:
                col_name = col[0]
                col_type = col[1].lower()
                if any(t in col_type for t in ['int', 'float', 'double', 'decimal', 'numeric']):
                    numeric_columns.append(col_name)
            
            if not numeric_columns:
                return None
            
            print(f"         📋 Found {len(numeric_columns)} numeric columns")
            
            # Search for our distinctive values
            found_values = []
            for value in self.distinctive_values:
                for col in numeric_columns:
                    try:
                        query = f"SELECT COUNT(*) FROM {table_name} WHERE {col} = {value}"
                        self.cursor.execute(query)
                        count = self.cursor.fetchone()[0]
                        
                        if count > 0:
                            found_values.append((value, col, count))
                            print(f"         ✅ Found {value} in column {col} ({count} rows)")
                            
                    except Exception as e:
                        continue  # Skip columns that cause errors
            
            if len(found_values) >= 3:  # If we found at least 3 distinctive values
                print(f"      🎯 PROMISING TABLE! Found {len(found_values)} distinctive values")
                return {
                    'table': table_name,
                    'found_values': found_values,
                    'numeric_columns': numeric_columns
                }
            
            return None
            
        except Exception as e:
            print(f"      ❌ Error analyzing {table_name}: {e}")
            return None
    
    def verify_sot_matches(self, table_info):
        """Verify if this table matches our SOT records"""
        table_name = table_info['table']
        print(f"\n🎯 VERIFYING SOT MATCHES FOR: {table_name}")
        print("="*60)
        
        try:
            # Get table schema to identify columns
            self.cursor.execute(f"DESCRIBE {table_name}")
            columns = [row[0].lower() for row in self.cursor.fetchall()]
            
            # Try to identify key columns
            plant_col = None
            storage_col = None
            material_col = None
            ext_total_col = None
            
            # Look for plant column
            for col in columns:
                if any(keyword in col for keyword in ['plant', 'werk']):
                    plant_col = col
                    break
            
            # Look for storage location column
            for col in columns:
                if any(keyword in col for keyword in ['storage', 'location', 'lgort']):
                    storage_col = col
                    break
            
            # Look for material column
            for col in columns:
                if any(keyword in col for keyword in ['material', 'matnr']):
                    material_col = col
                    break
            
            # Look for ext total column (column that had our distinctive values)
            value_columns = [item[1] for item in table_info['found_values']]
            ext_total_col = max(set(value_columns), key=value_columns.count)  # Most frequent column
            
            print(f"   Identified columns:")
            print(f"   Plant: {plant_col}")
            print(f"   Storage: {storage_col}")
            print(f"   Material: {material_col}")
            print(f"   Ext Total (likely): {ext_total_col}")
            
            if not (plant_col and material_col and ext_total_col):
                print("❌ Cannot identify required columns")
                return None
            
            # Check SOT records
            matches = 0
            for plant, storage, material, sot_sap, sot_ext, sot_diff in self.sot_records:
                where_conditions = [f"{plant_col} = '{plant}'", f"{material_col} = '{material}'"]
                if storage_col:
                    where_conditions.append(f"{storage_col} = '{storage}'")
                
                where_clause = " AND ".join(where_conditions)
                query = f"SELECT {ext_total_col} FROM {table_name} WHERE {where_clause}"
                
                try:
                    self.cursor.execute(query)
                    result = self.cursor.fetchone()
                    
                    if result:
                        db_ext = result[0]
                        print(f"\n📋 {plant} | {storage} | {material}")
                        print(f"   SOT: {sot_ext}, DB: {db_ext}")
                        
                        if abs(float(db_ext) - float(sot_ext)) < 0.01:
                            print(f"   ✅ PERFECT MATCH!")
                            matches += 1
                        else:
                            print(f"   ❌ MISMATCH!")
                    else:
                        print(f"\n📋 {plant} | {storage} | {material} - No record found")
                        
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                    continue
            
            match_rate = matches / len(self.sot_records)
            print(f"\n📊 MATCH SUMMARY: {matches}/{len(self.sot_records)} ({match_rate:.1%})")
            
            if match_rate >= 0.7:  # 70% match rate
                print(f"\n🏆 FOUND THE EXT TOTAL TABLE!")
                return {
                    'table': table_name,
                    'ext_total_column': ext_total_col,
                    'match_rate': match_rate,
                    'matches': matches
                }
            
            return None
            
        except Exception as e:
            print(f"❌ Error verifying SOT matches: {e}")
            return None
    
    def search(self):
        """Main search function"""
        print("🎯 FOCUSED EXT TOTAL SEARCH")
        print("="*60)
        
        if not self.connect():
            return None
        
        all_promising_tables = []
        
        # Search target databases
        for db_name in self.target_databases:
            promising_tables = self.search_specific_database(db_name)
            all_promising_tables.extend(promising_tables)
        
        print(f"\n📊 SEARCH COMPLETE")
        print(f"Found {len(all_promising_tables)} promising tables")
        
        # Verify the most promising tables
        best_match = None
        best_score = 0
        
        for table_info in all_promising_tables:
            verification = self.verify_sot_matches(table_info)
            if verification and verification['match_rate'] > best_score:
                best_score = verification['match_rate']
                best_match = verification
        
        if best_match:
            print(f"\n🏆 FINAL RESULT:")
            print(f"   Table: {best_match['table']}")
            print(f"   Ext Total Column: {best_match['ext_total_column']}")
            print(f"   Match Rate: {best_match['match_rate']:.1%}")
            return best_match
        else:
            print(f"\n❌ No perfect matches found")
            if all_promising_tables:
                print(f"Promising tables to check manually:")
                for table in all_promising_tables:
                    print(f"   - {table['table']}")
            return all_promising_tables
    
    def close(self):
        """Close connection"""
        if self.connection:
            self.connection.close()

def main():
    searcher = FocusedExtTotalSearch()
    try:
        result = searcher.search()
        if result and isinstance(result, dict) and 'table' in result:
            print(f"\n✅ SUCCESS! Found your Ext Total data in: {result['table']}")
        else:
            print(f"\n⚠️ No perfect match found")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        searcher.close()

if __name__ == "__main__":
    main()
